import { Card, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { cn } from "@/lib/utils";
import type { AthleteFormData } from "@/hooks/athletes/useAthleteForm";
import { format, parseISO } from 'date-fns';

interface AthleteBasicFormProps {
  formData: AthleteFormData;
  onFormDataChange: (data: AthleteFormData) => void;
  errors: Record<string, string>;
}

export function AthleteBasicForm({ formData, onFormDataChange, errors }: AthleteBasicFormProps) {
  const { t } = useSafeTranslation();

  const updateField = <K extends keyof AthleteFormData>(
    field: K,
    value: AthleteFormData[K]
  ) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    });
  };

  const updateParentField = <K extends keyof AthleteFormData['parent']>(
    field: K,
    value: AthleteFormData['parent'][K]
  ) => {
    onFormDataChange({
      ...formData,
      parent: {
        ...formData.parent,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Athlete Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('athletes.form.athleteInfo')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                {t('athletes.form.name')} *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateField('name', e.target.value)}
                className={cn(errors.name && "border-red-500")}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="surname">
                {t('athletes.form.surname')} *
              </Label>
              <Input
                id="surname"
                value={formData.surname}
                onChange={(e) => updateField('surname', e.target.value)}
                className={cn(errors.surname && "border-red-500")}
              />
              {errors.surname && (
                <p className="text-sm text-red-500">{errors.surname}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="nationalId">
                {t('athletes.form.nationalId')} *
              </Label>
              <Input
                id="nationalId"
                value={formData.nationalId}
                onChange={(e) => updateField('nationalId', e.target.value)}
                className={cn(errors.nationalId && "border-red-500")}
              />
              {errors.nationalId && (
                <p className="text-sm text-red-500">{errors.nationalId}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="birthDate">
                {t('athletes.form.birthDate')} *
              </Label>
              <DatePicker
                date={formData.birthDate ? parseISO(formData.birthDate) : undefined}
                onSelect={(date: Date | undefined) => {
                  if (date) {
                    // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
                    updateField('birthDate', format(date, 'yyyy-MM-dd'));
                  } else {
                    updateField('birthDate', '');
                  }
                }}
                placeholder={t('athletes.form.selectBirthDate')}
                className={cn(errors.birthDate && "border-red-500")}
              />
              {errors.birthDate && (
                <p className="text-sm text-red-500">{errors.birthDate}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="registrationDate">
                {t('athletes.form.registrationDate')} *
              </Label>
              <DatePicker
                date={formData.registrationDate ? new Date(formData.registrationDate) : undefined}
                onSelect={(date: Date | undefined) => {
                  if (date) {
                    // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    updateField('registrationDate', `${year}-${month}-${day}`);
                  } else {
                    updateField('registrationDate', '');
                  }
                }}
                placeholder={t('athletes.form.selectRegistrationDate')}
                className={cn(errors.registrationDate && "border-red-500")}
              />
              {errors.registrationDate && (
                <p className="text-sm text-red-500">{errors.registrationDate}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parent Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('athletes.form.parentInfo')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="parentName">
                {t('athletes.form.parentName')}
              </Label>
              <Input
                id="parentName"
                value={formData.parent.name}
                onChange={(e) => updateParentField('name', e.target.value)}
                className={cn(errors['parent.name'] && "border-red-500")}
              />
              {errors['parent.name'] && (
                <p className="text-sm text-red-500">{errors['parent.name']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentSurname">
                {t('athletes.form.parentSurname')}
              </Label>
              <Input
                id="parentSurname"
                value={formData.parent.surname}
                onChange={(e) => updateParentField('surname', e.target.value)}
                className={cn(errors['parent.surname'] && "border-red-500")}
              />
              {errors['parent.surname'] && (
                <p className="text-sm text-red-500">{errors['parent.surname']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentPhone">
                {t('athletes.form.parentPhone')}
              </Label>
              <Input
                id="parentPhone"
                value={formData.parent.phone}
                onChange={(e) => updateParentField('phone', e.target.value)}
                className={cn(errors['parent.phone'] && "border-red-500")}
              />
              {errors['parent.phone'] && (
                <p className="text-sm text-red-500">{errors['parent.phone']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentEmail">
                {t('athletes.form.parentEmail')}
              </Label>
              <Input
                id="parentEmail"
                type="email"
                value={formData.parent.email}
                onChange={(e) => updateParentField('email', e.target.value)}
                className={cn(errors['parent.email'] && "border-red-500")}
              />
              {errors['parent.email'] && (
                <p className="text-sm text-red-500">{errors['parent.email']}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="parentAddress">
                {t('athletes.form.parentAddress')}
              </Label>
              <Input
                id="parentAddress"
                value={formData.parent.address}
                onChange={(e) => updateParentField('address', e.target.value)}
                className={cn(errors['parent.address'] && "border-red-500")}
              />
              {errors['parent.address'] && (
                <p className="text-sm text-red-500">{errors['parent.address']}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
