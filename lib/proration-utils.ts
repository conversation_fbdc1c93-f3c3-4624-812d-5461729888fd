import { getYear, getMonth, getDate, getDaysInMonth } from 'date-fns';

/**
 * Utility functions for calculating prorated payment amounts
 */

/**
 * Calculate prorated amount for remaining days in the current month
 * @param monthlyAmount - The full monthly payment amount
 * @param startDate - The date to start proration from (defaults to today)
 * @returns The prorated amount for remaining days
 */
export function calculateProratedAmount(
  monthlyAmount: number,
  startDate: Date = new Date()
): number {
  const currentDate = startDate;

  // Get total days in the current month
  const totalDaysInMonth = getDaysInMonth(currentDate);

  // Get remaining days (including today)
  const currentDay = getDate(currentDate);
  const remainingDays = totalDaysInMonth - currentDay + 1;

  // Calculate daily rate and multiply by remaining days
  const dailyRate = monthlyAmount / totalDaysInMonth;
  const proratedAmount = dailyRate * remainingDays;

  return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
}

/**
 * Get the number of remaining days in the current month (including today)
 * @param startDate - The date to calculate from (defaults to today)
 * @returns Number of remaining days
 */
export function getRemainingDaysInMonth(startDate: Date = new Date()): number {
  const currentDate = startDate;

  // Get total days in the current month
  const totalDaysInMonth = getDaysInMonth(currentDate);

  // Get remaining days (including today)
  const currentDay = getDate(currentDate);
  const remainingDays = totalDaysInMonth - currentDay + 1;

  return remainingDays;
}

/**
 * Get total days in the current month
 * @param date - The date to get the month from (defaults to today)
 * @returns Total days in the month
 */
export function getTotalDaysInMonth(date: Date = new Date()): number {
  return getDaysInMonth(date);
}
