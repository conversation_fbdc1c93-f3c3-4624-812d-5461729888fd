import { db } from "@/src/db";
import { athletePaymentPlans, payments, athletes, paymentPlans, teams } from "@/src/db/schema";
import { and, eq, isNull, or, gte, lt } from "drizzle-orm";
import { calculateAthleteBalance, updateAthleteBalance } from "../balance-calculator";
import { format, getYear, getMonth, addMonths, setDate, isBefore } from 'date-fns';

interface PaymentProcessingResult {
  totalAssignments: number;
  paymentsCreated: number;
  errors?: string[];
}

/**
 * Check if a payment already exists for the athlete's payment plan assignment for the current month
 */
async function hasExistingPaymentForMonth(
  athleteId: string,
  assignmentId: string,
  targetDate: string
): Promise<boolean> {
  const targetDateObj = new Date(targetDate);
  const currentYear = getYear(targetDateObj);
  const currentMonth = getMonth(targetDateObj) + 1; // getMonth() returns 0-11

  // Create month boundaries
  const monthStart = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;
  const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
  const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
  const monthEnd = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01`;

  const existingPayment = await db
    .select({ id: payments.id })
    .from(payments)
    .where(
      and(
        eq(payments.athleteId, athleteId),
        eq(payments.athletePaymentPlanId, assignmentId),
        gte(payments.date, monthStart),
        lt(payments.date, monthEnd)
      )
    )
    .limit(1);

  return existingPayment.length > 0;
}

/**
 * Creates pending payments for athletes whose payment plans are assigned today
 */
export async function createPendingPaymentsForToday(): Promise<PaymentProcessingResult> {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const currentDay = new Date().getDate();
  
  console.log(`Processing payments for date: ${today}, day of month: ${currentDay}`);
  
  const errors: string[] = [];
  let paymentsCreated = 0;

  try {
    // Find all active payment plan assignments where:
    // 1. Assignment is active
    // 2. Assignment date matches today OR assign day matches current day of month
    // 3. No pending payment exists for this month
    const assignmentsToProcess = await db
      .select({
        assignmentId: athletePaymentPlans.id,
        athleteId: athletePaymentPlans.athleteId,
        planId: athletePaymentPlans.planId,
        teamId: athletePaymentPlans.teamId,
        tenantId: athletePaymentPlans.tenantId,
        assignedDate: athletePaymentPlans.assignedDate,
        athleteName: athletes.name,
        athleteSurname: athletes.surname,
        athleteStatus: athletes.status,
        planName: paymentPlans.name,
        monthlyValue: paymentPlans.monthlyValue,
        assignDay: paymentPlans.assignDay,
        dueDay: paymentPlans.dueDay,
        teamName: teams.name,
      })
      .from(athletePaymentPlans)
      .innerJoin(athletes, eq(athletePaymentPlans.athleteId, athletes.id))
      .innerJoin(paymentPlans, eq(athletePaymentPlans.planId, paymentPlans.id))
      .leftJoin(teams, eq(athletePaymentPlans.teamId, teams.id))
      .where(
        and(
          eq(athletePaymentPlans.isActive, true),
          eq(paymentPlans.status, 'active'),
          eq(athletes.status, 'active'), // Only process payments for active athletes
          eq(paymentPlans.assignDay, currentDay) // Recurring monthly assignment
        )
      );

    console.log(`Found ${assignmentsToProcess.length} assignments to process`);

    for (const assignment of assignmentsToProcess) {
      try {
        // Check if a payment already exists for this assignment in the current month
        // This prevents duplicate payments if the assignment day is changed after payment creation
        const hasExistingPayment = await hasExistingPaymentForMonth(
          assignment.athleteId, 
          assignment.assignmentId, 
          today
        );

        if (hasExistingPayment) {
          console.log(`Payment already exists for athlete ${assignment.athleteName} ${assignment.athleteSurname} - Plan: ${assignment.planName} for current month`);
          continue;
        }

        // Calculate due date
        let dueDate = setDate(new Date(), assignment.dueDay);

        // If due day has passed this month, set it for next month
        if (isBefore(dueDate, new Date())) {
          dueDate = addMonths(dueDate, 1);
        }

        // Create pending payment
        const [newPayment] = await db
          .insert(payments)
          .values({
            tenantId: assignment.tenantId,
            athleteId: assignment.athleteId,
            athletePaymentPlanId: assignment.assignmentId,
            amount: assignment.monthlyValue,
            date: today,
            dueDate: format(dueDate, 'yyyy-MM-dd'),
            status: 'pending',
            type: 'fee', // Payment plan payments are fees
            method: null, // Payment method not specified for scheduled payments
            description: `Monthly payment for ${assignment.planName}${assignment.teamName ? ` - ${assignment.teamName}` : ''}`,
            createdBy: BigInt(1), // System user - you might want to create a proper system user ID
            updatedBy: BigInt(1),
          })
          .returning();

        // Recalculate athlete balance after creating the payment
        if (assignment.tenantId) {
          await calculateAthleteBalance(assignment.athleteId, assignment.tenantId);
        }

        paymentsCreated++;
        console.log(`Created payment for ${assignment.athleteName} ${assignment.athleteSurname} - Amount: ${assignment.monthlyValue} TL - Due: ${dueDate.toISOString().split('T')[0]}`);

        // Update athlete balance after creating payment
        try {
          await updateAthleteBalance(assignment.athleteId, assignment.tenantId, BigInt(1));
        } catch (balanceError) {
          console.error(`Error updating balance for athlete ${assignment.athleteId}:`, balanceError);
          // Don't add to errors as payment was created successfully
        }

      } catch (error) {
        const errorMessage = `Failed to create payment for athlete ${assignment.athleteId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage);
        errors.push(errorMessage);
      }
    }

    const result: PaymentProcessingResult = {
      totalAssignments: assignmentsToProcess.length,
      paymentsCreated,
      errors: errors.length > 0 ? errors : undefined
    };

    return result;

  } catch (error) {
    console.error('Error in payment processing:', error);
    throw error;
  }
}

/**
 * Manual function to process payments for a specific date (useful for testing)
 */
export async function createPendingPaymentsForDate(targetDate: string): Promise<PaymentProcessingResult> {
  const targetDay = new Date(targetDate).getDate();
  
  console.log(`Processing payments for specific date: ${targetDate}, day of month: ${targetDay}`);
  
  const errors: string[] = [];
  let paymentsCreated = 0;

  try {
    const assignmentsToProcess = await db
      .select({
        assignmentId: athletePaymentPlans.id,
        athleteId: athletePaymentPlans.athleteId,
        planId: athletePaymentPlans.planId,
        teamId: athletePaymentPlans.teamId,
        tenantId: athletePaymentPlans.tenantId,
        assignedDate: athletePaymentPlans.assignedDate,
        athleteName: athletes.name,
        athleteSurname: athletes.surname,
        athleteStatus: athletes.status,
        planName: paymentPlans.name,
        monthlyValue: paymentPlans.monthlyValue,
        assignDay: paymentPlans.assignDay,
        dueDay: paymentPlans.dueDay,
        teamName: teams.name,
      })
      .from(athletePaymentPlans)
      .innerJoin(athletes, eq(athletePaymentPlans.athleteId, athletes.id))
      .innerJoin(paymentPlans, eq(athletePaymentPlans.planId, paymentPlans.id))
      .leftJoin(teams, eq(athletePaymentPlans.teamId, teams.id))
      .where(
        and(
          eq(athletePaymentPlans.isActive, true),
          eq(paymentPlans.status, 'active'),
          eq(athletes.status, 'active'),
          or(
            eq(athletePaymentPlans.assignedDate, targetDate),
            eq(paymentPlans.assignDay, targetDay)
          )
        )
      );

    console.log(`Found ${assignmentsToProcess.length} assignments to process for ${targetDate}`);

    // Process assignments (same logic as above)
    for (const assignment of assignmentsToProcess) {
      try {
        // Check if a payment already exists for this assignment in the target month
        // This prevents duplicate payments if the assignment day is changed after payment creation
        const hasExistingPayment = await hasExistingPaymentForMonth(
          assignment.athleteId, 
          assignment.assignmentId, 
          targetDate
        );

        if (hasExistingPayment) {
          console.log(`Payment already exists for athlete ${assignment.athleteName} ${assignment.athleteSurname} - Plan: ${assignment.planName} for target month`);
          continue;
        }

        // Calculate due date based on target date
        const targetDateObj = new Date(targetDate);
        let dueDate = setDate(targetDateObj, assignment.dueDay);

        // If due day has passed this month, set it for next month
        if (isBefore(dueDate, targetDateObj)) {
          dueDate = addMonths(dueDate, 1);
        }

        // Create pending payment
        await db
          .insert(payments)
          .values({
            tenantId: assignment.tenantId,
            athleteId: assignment.athleteId,
            athletePaymentPlanId: assignment.assignmentId,
            amount: assignment.monthlyValue,
            date: targetDate,
            dueDate: format(dueDate, 'yyyy-MM-dd'),
            status: 'pending',
            type: 'fee',
            method: null, // Payment method not specified for scheduled payments
            description: `Monthly payment for ${assignment.planName}${assignment.teamName ? ` - ${assignment.teamName}` : ''}`,
            createdBy: BigInt(1),
            updatedBy: BigInt(1),
          });

        // Recalculate athlete balance after creating the payment
        if (assignment.tenantId) {
          await calculateAthleteBalance(assignment.athleteId, assignment.tenantId);
        }

        // Update athlete balance after creating payment
        try {
          const { updateAthleteBalance } = await import('../balance-calculator');
          await updateAthleteBalance(assignment.athleteId, assignment.tenantId, BigInt(1));
        } catch (balanceError) {
          console.error(`Error updating balance for athlete ${assignment.athleteId}:`, balanceError);
          // Don't add to errors as payment was created successfully
        }

        paymentsCreated++;
        console.log(`Created payment for ${assignment.athleteName} ${assignment.athleteSurname} - Amount: ${assignment.monthlyValue} TL - Due: ${dueDate.toISOString().split('T')[0]}`);

      } catch (error) {
        const errorMessage = `Failed to create payment for athlete ${assignment.athleteId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage);
        errors.push(errorMessage);
      }
    }

    return {
      totalAssignments: assignmentsToProcess.length,
      paymentsCreated,
      errors: errors.length > 0 ? errors : undefined
    };

  } catch (error) {
    console.error('Error in payment processing for specific date:', error);
    throw error;
  }
}
