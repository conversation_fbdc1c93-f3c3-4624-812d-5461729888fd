/**
 * Server-side utility functions
 * These utilities are specifically designed for server-side use only
 */

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

/**
 * Utility function for merging CSS classes (server-side version)
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format currency for server-side rendering
 */
export function formatCurrency(amount: string | number, locale: string = 'tr-TR'): string {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numericAmount);
}

import { parseISO, isValid } from 'date-fns';

/**
 * Format date for server-side rendering
 */
export function formatDate(date: Date | string, locale: string = 'tr-TR'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(dateObj)) {
    return '';
  }
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(dateObj);
}

/**
 * Format date and time for server-side rendering
 */
export function formatDateTime(date: Date | string, locale: string = 'tr-TR'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(dateObj)) {
    return '';
  }
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj);
}

/**
 * Get request headers (server-side only)
 */
export async function getRequestHeaders() {
  return await headers();
}

/**
 * Get client IP address from headers (server-side only)
 */
export async function getClientIP(): Promise<string | null> {
  const headersList = await headers();

  // Check various headers for IP address
  const forwardedFor = headersList.get('x-forwarded-for');
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim();
  }

  const realIP = headersList.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  const remoteAddr = headersList.get('remote-addr');
  if (remoteAddr) {
    return remoteAddr;
  }

  return null;
}

/**
 * Get user agent from headers (server-side only)
 */
export async function getUserAgent(): Promise<string | null> {
  const headersList = await headers();
  return headersList.get('user-agent');
}

/**
 * Check if request is from mobile device (server-side only)
 */
export async function isMobileDevice(): Promise<boolean> {
  const userAgent = await getUserAgent();
  if (!userAgent) return false;

  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  return mobileRegex.test(userAgent);
}

/**
 * Generate a secure random string (server-side only)
 */
export function generateSecureRandomString(length: number = 32): string {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Hash a string using SHA-256 (server-side only)
 */
export function hashString(input: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(input).digest('hex');
}

/**
 * Validate email format (server-side version)
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate Turkish phone number format (server-side version)
 */
export function isValidTurkishPhone(phone: string): boolean {
  const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * Validate Turkish national ID (server-side version)
 */
export function isValidTurkishNationalId(nationalId: string): boolean {
  if (!/^\d{11}$/.test(nationalId)) {
    return false;
  }
  
  // Turkish national ID validation algorithm
  const digits = nationalId.split('').map(Number);
  const checksum = digits[10];
  
  // Calculate checksum
  let sum1 = 0;
  let sum2 = 0;
  
  for (let i = 0; i < 9; i++) {
    if (i % 2 === 0) {
      sum1 += digits[i];
    } else {
      sum2 += digits[i];
    }
  }
  
  const calculatedChecksum = ((sum1 * 7) - sum2) % 10;
  return calculatedChecksum === checksum;
}

/**
 * Sanitize HTML content (server-side only)
 */
export function sanitizeHtml(html: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
}

/**
 * Parse and validate JSON safely (server-side only)
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

/**
 * Sleep function for server-side delays
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff (server-side only)
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      await sleep(delay);
    }
  }
  
  throw lastError!;
}

/**
 * Environment variable utilities (server-side only)
 */
export const env = {
  get: (key: string, fallback?: string): string | undefined => {
    return process.env[key] || fallback;
  },
  
  getRequired: (key: string): string => {
    const value = process.env[key];
    if (!value) {
      throw new Error(`Required environment variable ${key} is not set`);
    }
    return value;
  },
  
  getBoolean: (key: string, fallback: boolean = false): boolean => {
    const value = process.env[key];
    if (!value) return fallback;
    return value.toLowerCase() === 'true';
  },
  
  getNumber: (key: string, fallback?: number): number | undefined => {
    const value = process.env[key];
    if (!value) return fallback;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? fallback : parsed;
  },
};

/**
 * Redirect with proper error handling (server-side only)
 */
export function safeRedirect(url: string): never {
  try {
    redirect(url);
  } catch (error) {
    // Next.js redirect throws an error to stop execution
    throw error;
  }
}
